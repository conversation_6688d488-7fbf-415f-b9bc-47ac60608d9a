/* Copyright(c) 2009-2025 shenzhen TP-Link Technologies Co.Ltd.
 * 
 * file        Prime_number_enumeration.c
 * brief       Input a positive integer n, find the set of all prime numbers that are less than 
 *             or equal to n, and output them in rows of 10.
 * 
 * author      <PERSON>
 * version     1.0.0
 * date        30july25
 * 
 * history     \arg 1.0.0, 30<PERSON><PERSON><PERSON>, <PERSON>, Create the file.
 * 
 */
 
 #include <stdio.h>
 #include <stdbool.h>
 #include <math.h>
/************************************************************************************************/
/*                                               DEFINES                                        */
/************************************************************************************************/

/************************************************************************************************/
/*                                               TYPES                                          */
/************************************************************************************************/

/************************************************************************************************/
/*                                               EXTERN_PROTOTYPES                              */
/************************************************************************************************/

/************************************************************************************************/
/*                                               LOCAL_PROTOTYPES                               */
/************************************************************************************************/

/************************************************************************************************/
/*                                               VARIABLES                                      */
/************************************************************************************************/

/************************************************************************************************/
/*                                               LOCAL_FUNCTIONS                                */
/************************************************************************************************/

/************************************************************************************************/
/*                                               PUBLIC_FUNCTIONS                               */
/************************************************************************************************/
/*
 *fn           bool is_prime(int num)
 *brief        Determine whether The input value is a prime number.
 *
 * param[in]   num    A positive integer
 * 
 * return      Boolean value
*/
bool is_prime(int num){
    if (num <= 1){
        return false;
    }
    if (num == 2){
        return true;
    }
    if (num % 2 == 0){
        return false;
    }

    int sqrt_num = (int)sqrt(num) + 1;
    for (int i = 3; i <= sqrt_num;i += 2){
        if (num % i == 0){
            return false;
        }
    }
    return true;
}

/*
 *fn           void print_primes(int primes[], int count)
 *brief        Format and output prime numbers.
 *
 * param[in]   primes[]  Array of prime numbers
 * param[in]   count     The number of digits contained within the primes[]
 * 
*/
void print_primes(int primes[], int count){
    for (int i = 0; i < count; i++){
        printf("%3d", primes[i]);
        if (i != count - 1){
            printf(",");
        }
        if ((i + 1) % 10 == 0){
            printf("\n");
        }
    }
    printf("\n");
}
/************************************************************************************************/
/*                                               GLOBAL_FUNCTIONS                               */
/************************************************************************************************/

