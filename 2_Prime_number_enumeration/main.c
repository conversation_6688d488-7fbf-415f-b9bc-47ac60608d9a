/* Copyright(c) 2009-2025 shenzhen TP-Link Technologies Co.Ltd.
 * 
 * file        main.c
 * brief       Input a positive integer n, find the set of all prime numbers that are less than 
 *             or equal to n, and output them in rows of 10.
 * 
 * author      <PERSON>
 * version     1.0.0
 * date        30july25
 * 
 * history     \arg 1.0.0, 30ju<PERSON><PERSON>, <PERSON>, Create the file.
 * 
 */
 
 #include <stdio.h>
 #include <stdbool.h>
 #include <math.h>
 #include "Prime_number_enumeration.h"

/************************************************************************************************/
/*                                               DEFINES                                        */
/************************************************************************************************/
#define MAX 1000
/************************************************************************************************/
/*                                               TYPES                                          */
/************************************************************************************************/

/************************************************************************************************/
/*                                               EXTERN_PROTOTYPES                              */
/************************************************************************************************/

/************************************************************************************************/
/*                                               LOCAL_PROTOTYPES                               */
/************************************************************************************************/

/************************************************************************************************/
/*                                               VARIABLES                                      */
/************************************************************************************************/

/************************************************************************************************/
/*                                               LOCAL_FUNCTIONS                                */
/************************************************************************************************/

/************************************************************************************************/
/*                                               PUBLIC_FUNCTIONS                               */
/************************************************************************************************/

/************************************************************************************************/
/*                                               GLOBAL_FUNCTIONS                               */
/************************************************************************************************/
 int main(){
    int n;
    printf("Please enter a number between 2 and 1000: ");
    scanf("%d", &n);
    if (n < 2 || n > MAX){  /*Input validation*/
        printf("Error! Please enter a number between 2 and 1000.\n");
        return 1;
    }
    int primes[MAX] = {0};
    int prime_count = 0;
    for (int i = 2; i <= n; i++){  /*Store the found prime numbers in an array*/
        if (is_prime(i)){
            primes[prime_count++] = i;
        }
    }
    printf("Primes <= %d:\n", n);
    print_primes(primes, prime_count);
    return 0;
}