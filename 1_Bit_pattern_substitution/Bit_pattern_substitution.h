/* Copyright(c) 2009-2025 shenzhen TP-Link Technologies Co.Ltd.
 * 
 * file        Bit_pattern_substitution.h
 * brief       Replace the pattern 101 with 011 in the binary code of a 32-bit integer from high
 *             to low.
 * 
 * author      <PERSON>
 * version     1.0.0
 * date        30july25
 * 
 * history     \arg 1.0.0, 30ju<PERSON><PERSON>, <PERSON>, Create the file.
 * 
 */

#include <stdio.h>
/************************************************************************************************/
/*                                               DEFINES                                        */
/************************************************************************************************/

/************************************************************************************************/
/*                                               TYPES                                          */
/************************************************************************************************/

/************************************************************************************************/
/*                                               VARIABLES                                      */
/************************************************************************************************/

/************************************************************************************************/
/*                                               FUNCTIONS                                      */
/************************************************************************************************/
/*
 *fn           unsigned int pattern_replace(unsigned int input)
 *brief        Replace the pattern 101 with 011 in the binary code of a 32-bit integer from high
 *             to low.
 *
 * param[in]   input  Original 32-bit integer
 * param[out]  input  The 32-bit integer after pattern replacement
 * 
 * return      The integer value after mode replacement
*/
unsigned int pattern_replace(unsigned int input);